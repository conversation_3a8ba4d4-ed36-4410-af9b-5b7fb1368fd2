"""
使用外部库进行响应格式化
利用现有的成熟库而不是重复造轮子
"""

from typing import Dict, Any, Optional
import json

def format_with_rich(response_data: Dict[str, Any]) -> str:
    """使用Rich库格式化响应"""
    try:
        from rich.console import Console
        from rich.table import Table
        from rich.panel import Panel
        from rich.text import Text
        from rich import print_json
        from io import StringIO
        
        console = Console(file=StringIO(), width=80)
        
        # 创建表格显示关键信息
        table = Table(title="工具响应摘要", show_header=True, header_style="bold magenta")
        table.add_column("字段", style="cyan", no_wrap=True)
        table.add_column("值", style="green")
        
        # 提取关键字段
        key_fields = ['status', 'step_number', 'total_steps', 'confidence', 'findings']
        for field in key_fields:
            if field in response_data:
                value = str(response_data[field])
                if len(value) > 50:
                    value = value[:50] + "..."
                table.add_row(field, value)
        
        console.print(table)
        
        # 获取格式化后的输出
        output = console.file.getvalue()
        return output
        
    except ImportError:
        return "Rich库未安装，请运行: pip install rich"
    except Exception as e:
        return f"Rich格式化失败: {str(e)}"

def format_with_humanize(response_data: Dict[str, Any]) -> str:
    """使用Humanize库格式化数值和时间"""
    try:
        import humanize
        from datetime import datetime
        
        parts = []
        
        # 格式化数值
        if 'step_number' in response_data and 'total_steps' in response_data:
            current = response_data['step_number']
            total = response_data['total_steps']
            percentage = (current / total) * 100 if total > 0 else 0
            parts.append(f"📊 进度: {humanize.ordinal(current)} / {humanize.ordinal(total)} ({percentage:.1f}%)")
        
        # 格式化文件数量
        if 'files_checked' in response_data:
            file_count = len(response_data['files_checked'])
            parts.append(f"📄 检查文件: {humanize.intcomma(file_count)} 个")
        
        # 格式化问题数量
        if 'issues_found' in response_data:
            issue_count = len(response_data['issues_found'])
            if issue_count > 0:
                parts.append(f"⚠️ 发现问题: {humanize.intcomma(issue_count)} 个")
            else:
                parts.append("✅ 未发现问题")
        
        # 主要内容
        if 'findings' in response_data:
            findings = response_data['findings']
            if len(findings) > 100:
                findings = findings[:100] + "..."
            parts.append(f"🔍 发现: {findings}")
        
        return '\n'.join(parts) if parts else "无关键信息"
        
    except ImportError:
        return "Humanize库未安装，请运行: pip install humanize"
    except Exception as e:
        return f"Humanize格式化失败: {str(e)}"

def format_with_prettytable(response_data: Dict[str, Any]) -> str:
    """使用PrettyTable库创建表格"""
    try:
        from prettytable import PrettyTable
        
        table = PrettyTable()
        table.field_names = ["属性", "值"]
        table.align["属性"] = "l"
        table.align["值"] = "l"
        table.max_width["值"] = 50
        
        # 添加关键信息到表格
        key_info = [
            ('状态', response_data.get('status', '未知')),
            ('置信度', response_data.get('confidence', '未知')),
            ('步骤', f"{response_data.get('step_number', 0)}/{response_data.get('total_steps', 0)}"),
        ]
        
        for key, value in key_info:
            if value and str(value) != '未知' and str(value) != '0/0':
                table.add_row([key, str(value)])
        
        # 添加发现
        findings = response_data.get('findings', '')
        if findings:
            if len(findings) > 100:
                findings = findings[:100] + "..."
            table.add_row(['发现', findings])
        
        return str(table)
        
    except ImportError:
        return "PrettyTable库未安装，请运行: pip install prettytable"
    except Exception as e:
        return f"PrettyTable格式化失败: {str(e)}"

def smart_format(response_data: Dict[str, Any], prefer_library: str = 'auto') -> str:
    """
    智能选择最佳的格式化库
    
    Args:
        response_data: 要格式化的数据
        prefer_library: 首选库 ('rich', 'humanize', 'prettytable', 'auto')
    
    Returns:
        格式化后的字符串
    """
    if prefer_library == 'rich':
        return format_with_rich(response_data)
    elif prefer_library == 'humanize':
        return format_with_humanize(response_data)
    elif prefer_library == 'prettytable':
        return format_with_prettytable(response_data)
    else:
        # 自动选择最佳库
        # 优先级: Rich > Humanize > PrettyTable > 简单格式
        
        # 尝试Rich
        try:
            import rich
            return format_with_rich(response_data)
        except ImportError:
            pass
        
        # 尝试Humanize
        try:
            import humanize
            return format_with_humanize(response_data)
        except ImportError:
            pass
        
        # 尝试PrettyTable
        try:
            import prettytable
            return format_with_prettytable(response_data)
        except ImportError:
            pass
        
        # 回退到简单格式
        return simple_format(response_data)

def simple_format(response_data: Dict[str, Any]) -> str:
    """简单格式化，不依赖外部库"""
    parts = []
    
    # 状态
    status = response_data.get('status', '')
    if status:
        parts.append(f"状态: {status}")
    
    # 进度
    step = response_data.get('step_number', 0)
    total = response_data.get('total_steps', 0)
    if step and total:
        parts.append(f"进度: {step}/{total}")
    
    # 发现
    findings = response_data.get('findings', '')
    if findings:
        if len(findings) > 150:
            findings = findings[:150] + "..."
        parts.append(f"发现: {findings}")
    
    return '\n'.join(parts) if parts else "无关键信息"

# 便捷函数
def format_response(response_data: Dict[str, Any], library: str = 'auto') -> str:
    """
    格式化工具响应的便捷函数
    
    Args:
        response_data: JSON响应数据
        library: 使用的库 ('auto', 'rich', 'humanize', 'prettytable')
    
    Returns:
        格式化后的自然语言描述
    """
    if isinstance(response_data, str):
        try:
            response_data = json.loads(response_data)
        except json.JSONDecodeError:
            return f"JSON解析失败: {response_data}"
    
    return smart_format(response_data, library)
