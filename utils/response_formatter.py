"""
自然语言响应格式化工具
将复杂的JSON响应转换为简洁易懂的自然语言描述
"""

import json
import re
from typing import Dict, Any, Optional
from datetime import datetime


class NaturalLanguageFormatter:
    """将工具的JSON响应转换为自然语言的格式化器"""
    
    def __init__(self):
        self.emoji_map = {
            'success': '✅',
            'error': '❌', 
            'warning': '⚠️',
            'info': 'ℹ️',
            'thinking': '💭',
            'analysis': '🔍',
            'code': '💻',
            'file': '📄',
            'progress': '📊',
            'next': '➡️',
            'complete': '🎉',
            'pending': '⏳'
        }
    
    def format_thinkdeep_response(self, response_data: Dict[str, Any]) -> str:
        """格式化thinkdeep工具的响应"""
        try:
            # 解析JSON响应
            if isinstance(response_data, str):
                data = json.loads(response_data)
            else:
                data = response_data
            
            # 提取关键信息
            step_number = data.get('step_number', 0)
            total_steps = data.get('total_steps', 0)
            findings = data.get('findings', '')
            confidence = data.get('confidence', 'unknown')
            next_step = data.get('next_step_required', False)
            
            # 构建自然语言描述
            parts = []
            
            # 进度信息
            if step_number and total_steps:
                parts.append(f"{self.emoji_map['progress']} **思考进度**: 第{step_number}步/共{total_steps}步")
            
            # 核心发现
            if findings:
                parts.append(f"{self.emoji_map['analysis']} **发现**: {findings}")
            
            # 置信度
            confidence_text = self._format_confidence(confidence)
            if confidence_text:
                parts.append(f"{self.emoji_map['info']} **置信度**: {confidence_text}")
            
            # 下一步
            if next_step:
                parts.append(f"{self.emoji_map['next']} **状态**: 继续深入分析中...")
            else:
                parts.append(f"{self.emoji_map['complete']} **状态**: 分析完成")
            
            return '\n'.join(parts)
            
        except Exception as e:
            return f"{self.emoji_map['error']} 响应格式化失败: {str(e)}"
    
    def format_codereview_response(self, response_data: Dict[str, Any]) -> str:
        """格式化代码审查响应"""
        try:
            if isinstance(response_data, str):
                data = json.loads(response_data)
            else:
                data = response_data
            
            parts = []
            
            # 审查状态
            status = data.get('status', 'unknown')
            parts.append(f"{self.emoji_map['code']} **代码审查状态**: {self._format_status(status)}")
            
            # 发现的问题
            issues = data.get('issues_found', [])
            if issues:
                parts.append(f"{self.emoji_map['warning']} **发现问题**: {len(issues)}个")
                for issue in issues[:3]:  # 只显示前3个
                    severity = issue.get('severity', 'unknown')
                    desc = issue.get('description', '未知问题')
                    parts.append(f"  • {severity.upper()}: {desc}")
                if len(issues) > 3:
                    parts.append(f"  • ...还有{len(issues)-3}个问题")
            
            # 检查的文件
            files_checked = data.get('files_checked', [])
            if files_checked:
                parts.append(f"{self.emoji_map['file']} **检查文件**: {len(files_checked)}个文件")
            
            return '\n'.join(parts)
            
        except Exception as e:
            return f"{self.emoji_map['error']} 代码审查响应格式化失败: {str(e)}"
    
    def format_debug_response(self, response_data: Dict[str, Any]) -> str:
        """格式化调试响应"""
        try:
            if isinstance(response_data, str):
                data = json.loads(response_data)
            else:
                data = response_data
            
            parts = []
            
            # 调试状态
            hypothesis = data.get('hypothesis', '')
            if hypothesis:
                parts.append(f"{self.emoji_map['thinking']} **当前假设**: {hypothesis}")
            
            # 置信度
            confidence = data.get('confidence', 'unknown')
            confidence_text = self._format_confidence(confidence)
            if confidence_text:
                parts.append(f"{self.emoji_map['info']} **置信度**: {confidence_text}")
            
            # 发现
            findings = data.get('findings', '')
            if findings:
                parts.append(f"{self.emoji_map['analysis']} **调试发现**: {findings}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            return f"{self.emoji_map['error']} 调试响应格式化失败: {str(e)}"
    
    def format_generic_response(self, response_data: Dict[str, Any]) -> str:
        """通用响应格式化"""
        try:
            if isinstance(response_data, str):
                data = json.loads(response_data)
            else:
                data = response_data
            
            parts = []
            
            # 状态
            status = data.get('status', 'unknown')
            parts.append(f"{self.emoji_map['info']} **状态**: {self._format_status(status)}")
            
            # 主要内容
            content = data.get('content', data.get('findings', ''))
            if content:
                # 截断过长的内容
                if len(content) > 200:
                    content = content[:200] + "..."
                parts.append(f"{self.emoji_map['analysis']} **内容**: {content}")
            
            # 元数据
            metadata = data.get('metadata', {})
            if metadata:
                model_used = metadata.get('model_used', '')
                if model_used:
                    parts.append(f"{self.emoji_map['info']} **使用模型**: {model_used}")
            
            return '\n'.join(parts)
            
        except Exception as e:
            return f"{self.emoji_map['error']} 通用响应格式化失败: {str(e)}"
    
    def _format_confidence(self, confidence: str) -> str:
        """格式化置信度"""
        confidence_map = {
            'exploring': '🔍 探索中',
            'low': '🟡 较低',
            'medium': '🟠 中等', 
            'high': '🟢 较高',
            'very_high': '💚 很高',
            'almost_certain': '✅ 接近确定',
            'certain': '🎯 确定'
        }
        return confidence_map.get(confidence.lower(), confidence)
    
    def _format_status(self, status: str) -> str:
        """格式化状态"""
        status_map = {
            'success': '✅ 成功',
            'error': '❌ 错误',
            'pending': '⏳ 进行中',
            'complete': '🎉 完成',
            'analysis_complete': '🔍 分析完成',
            'pause_for_thinkdeep': '💭 深度思考中',
            'continuation_available': '➡️ 可继续'
        }
        return status_map.get(status.lower(), status)
    
    def auto_format(self, response_data: Dict[str, Any], tool_name: str = '') -> str:
        """根据工具类型自动选择格式化方法"""
        tool_name = tool_name.lower()
        
        if 'thinkdeep' in tool_name:
            return self.format_thinkdeep_response(response_data)
        elif 'codereview' in tool_name:
            return self.format_codereview_response(response_data)
        elif 'debug' in tool_name:
            return self.format_debug_response(response_data)
        else:
            return self.format_generic_response(response_data)


# 全局格式化器实例
formatter = NaturalLanguageFormatter()


def format_tool_response(response_data: Dict[str, Any], tool_name: str = '') -> str:
    """
    便捷函数：将工具响应格式化为自然语言
    
    Args:
        response_data: 工具返回的JSON数据
        tool_name: 工具名称，用于选择合适的格式化方法
    
    Returns:
        格式化后的自然语言描述
    """
    return formatter.auto_format(response_data, tool_name)


def extract_key_info(response_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    从复杂响应中提取关键信息
    
    Args:
        response_data: 原始响应数据
    
    Returns:
        提取的关键信息字典
    """
    key_info = {}
    
    # 提取常见的关键字段
    common_fields = [
        'status', 'findings', 'confidence', 'step_number', 'total_steps',
        'hypothesis', 'issues_found', 'files_checked', 'content'
    ]
    
    for field in common_fields:
        if field in response_data:
            key_info[field] = response_data[field]
    
    return key_info
