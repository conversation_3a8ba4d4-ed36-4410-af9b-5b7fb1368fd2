"""
基于Jinja2的JSON到自然语言格式化器
使用成熟的模板引擎而不是重复造轮子
"""

import json
from typing import Dict, Any, Optional
from jinja2 import Template, Environment, BaseLoader
import re


class NaturalLanguageTemplates:
    """自然语言模板集合"""
    
    # ThinkDeep工具模板
    THINKDEEP_TEMPLATE = """
{%- if expert_analysis and expert_analysis.summary -%}
{{ expert_analysis.summary | truncate(500) }}
{%- elif expert_analysis and expert_analysis.content -%}
{{ expert_analysis.content | truncate(500) }}
{%- elif complete_thinkdeep and complete_thinkdeep.findings -%}
{{ complete_thinkdeep.findings | truncate(400) }}
{%- elif complete_thinkdeep and complete_thinkdeep.work_summary -%}
{%- set summary_lines = complete_thinkdeep.work_summary.split('\n') -%}
{%- set step_contents = [] -%}
{%- for line in summary_lines -%}
{%- if 'Step' in line and ':' in line -%}
{%- set step_content = line.split(':', 1)[1].strip() -%}
{%- if step_content and step_content not in step_contents -%}
{%- set _ = step_contents.append(step_content) -%}
{%- endif -%}
{%- endif -%}
{%- endfor -%}
{%- if step_contents -%}
{{ step_contents | join('\n\n') }}
{%- else -%}
{{ complete_thinkdeep.work_summary }}
{%- endif -%}
{%- elif next_steps -%}
{{ next_steps | truncate(300) }}
{%- else -%}
分析完成，暂无具体结果输出。
{%- endif -%}
""".strip()

    # 代码审查模板
    CODEREVIEW_TEMPLATE = """
{%- if step_number and total_steps -%}
📊 **审查进度**: 第{{ step_number }}步/共{{ total_steps }}步
{% endif -%}
{%- if files_checked -%}
📄 **检查文件**: {{ files_checked | length }}个文件
{% endif -%}
{%- if issues_found -%}
{%- if issues_found | length > 0 -%}
⚠️ **发现问题**: {{ issues_found | length }}个
{%- for issue in issues_found[:3] -%}
  • {{ issue.severity | upper }}: {{ issue.description | truncate(80) }}
{% endfor -%}
{%- if issues_found | length > 3 -%}
  • ...还有{{ issues_found | length - 3 }}个问题
{%- endif -%}
{%- else -%}
✅ **代码质量**: 未发现问题
{%- endif -%}
{%- endif -%}
{%- if findings -%}
🔍 **总结**: {{ findings | truncate(150) }}
{%- endif -%}
""".strip()

    # 调试工具模板
    DEBUG_TEMPLATE = """
{%- if step_number and total_steps -%}
📊 **调试进度**: 第{{ step_number }}步/共{{ total_steps }}步
{% endif -%}
{%- if hypothesis -%}
💭 **当前假设**: {{ hypothesis | truncate(150) }}
{% endif -%}
{%- if confidence -%}
💡 **置信度**: {{ confidence | confidence_format }}
{% endif -%}
{%- if findings -%}
🔍 **调试发现**: {{ findings | truncate(500) }}
{% endif -%}
{%- if expert_analysis -%}
🎯 **专家分析**: {{ expert_analysis | truncate(800) }}
{% endif -%}
{%- if status and status != 'debug_in_progress' -%}
ℹ️ **状态**: {{ status | status_format }}
{% endif -%}
""".strip()

    # 分析工具模板
    ANALYZE_TEMPLATE = """
{%- if step_number and total_steps -%}
📊 **分析进度**: 第{{ step_number }}步/共{{ total_steps }}步
{% endif -%}
{%- if confidence -%}
💡 **置信度**: {{ confidence | confidence_format }}
{% endif -%}
{%- if findings -%}
🔍 **分析发现**: {{ findings | truncate(500) }}
{% endif -%}
{%- if expert_analysis -%}
🎯 **专家分析**: {{ expert_analysis | truncate(800) }}
{% endif -%}
{%- if status and status != 'analyze_in_progress' -%}
ℹ️ **状态**: {{ status | status_format }}
{% endif -%}
""".strip()

    # 通用模板
    GENERIC_TEMPLATE = """
{%- if status -%}
ℹ️ **状态**: {{ status | status_format }}
{% endif -%}
{%- if error -%}
{%- if "参数错误" in error -%}
{{ error }}
{%- else -%}
❌ **错误**: {{ error | truncate(300) }}
{%- endif -%}
{% endif -%}
{%- if step_number and total_steps -%}
📊 **进度**: 第{{ step_number }}步/共{{ total_steps }}步
{% endif -%}
{%- if findings or content -%}
🔍 **内容**: {{ (findings or content) | truncate(200) }}
{% endif -%}
{%- if metadata and metadata.model_used -%}
🤖 **模型**: {{ metadata.model_used }}
{%- endif -%}
""".strip()

    # 列表模型模板
    LISTMODELS_TEMPLATE = """
{%- if content -%}
{{ content }}
{%- elif metadata and metadata.configured_providers -%}
🤖 **已配置提供商**: {{ metadata.configured_providers }}个
📋 **模型列表**: 请查看完整输出获取详细信息
{%- else -%}
📋 **模型列表**: 正在加载模型信息...
{%- endif -%}
""".strip()


class JinjaFormatter:
    """基于Jinja2的JSON响应格式化器"""
    
    def __init__(self):
        self.env = Environment(loader=BaseLoader())
        self._setup_filters()
        self.templates = NaturalLanguageTemplates()
    
    def _setup_filters(self):
        """设置自定义过滤器"""
        
        def confidence_format(confidence):
            """格式化置信度"""
            confidence_map = {
                'exploring': '🔍 探索中',
                'low': '🟡 较低',
                'medium': '🟠 中等',
                'high': '🟢 较高',
                'very_high': '💚 很高',
                'almost_certain': '✅ 接近确定',
                'certain': '🎯 确定'
            }
            return confidence_map.get(str(confidence).lower(), confidence)
        
        def status_format(status):
            """格式化状态"""
            status_map = {
                'success': '✅ 成功',
                'error': '❌ 错误',
                'pending': '⏳ 进行中',
                'complete': '🎉 完成',
                'analysis_complete': '🔍 分析完成',
                'pause_for_thinkdeep': '💭 深度思考中',
                'continuation_available': '➡️ 可继续'
            }
            return status_map.get(str(status).lower(), status)
        
        def truncate(text, length=150):
            """截断文本"""
            if not text:
                return ""
            text = str(text)
            if len(text) <= length:
                return text
            return text[:length] + "..."
        
        # 注册过滤器
        self.env.filters['confidence_format'] = confidence_format
        self.env.filters['status_format'] = status_format
        self.env.filters['truncate'] = truncate
    
    def format_thinkdeep(self, data: Dict[str, Any]) -> str:
        """格式化thinkdeep响应"""
        template = self.env.from_string(self.templates.THINKDEEP_TEMPLATE)
        return template.render(**data).strip()
    
    def format_codereview(self, data: Dict[str, Any]) -> str:
        """格式化代码审查响应"""
        template = self.env.from_string(self.templates.CODEREVIEW_TEMPLATE)
        return template.render(**data).strip()
    
    def format_debug(self, data: Dict[str, Any]) -> str:
        """格式化调试响应"""
        template = self.env.from_string(self.templates.DEBUG_TEMPLATE)
        return template.render(**data).strip()

    def format_analyze(self, data: Dict[str, Any]) -> str:
        """格式化分析响应"""
        template = self.env.from_string(self.templates.ANALYZE_TEMPLATE)
        return template.render(**data).strip()

    def format_listmodels(self, data: Dict[str, Any]) -> str:
        """格式化模型列表响应"""
        template = self.env.from_string(self.templates.LISTMODELS_TEMPLATE)
        return template.render(**data).strip()
    
    def format_generic(self, data: Dict[str, Any]) -> str:
        """通用格式化"""
        template = self.env.from_string(self.templates.GENERIC_TEMPLATE)
        return template.render(**data).strip()
    
    def auto_format(self, data: Dict[str, Any], tool_name: str = '') -> str:
        """根据工具类型自动选择模板"""
        tool_name = tool_name.lower()
        
        try:
            if 'thinkdeep' in tool_name:
                return self.format_thinkdeep(data)
            elif 'codereview' in tool_name:
                return self.format_codereview(data)
            elif 'debug' in tool_name:
                return self.format_debug(data)
            elif 'analyze' in tool_name:
                return self.format_analyze(data)
            elif 'listmodels' in tool_name:
                return self.format_listmodels(data)
            else:
                return self.format_generic(data)
        except Exception as e:
            # 回退到简单格式
            return self._simple_fallback(data, str(e))
    
    def _simple_fallback(self, data: Dict[str, Any], error: str = '') -> str:
        """简单回退格式化"""
        parts = []
        
        if error:
            parts.append(f"⚠️ 模板渲染失败: {error}")
        
        # 提取关键信息
        if 'status' in data:
            parts.append(f"状态: {data['status']}")
        
        if 'step_number' in data and 'total_steps' in data:
            parts.append(f"进度: {data['step_number']}/{data['total_steps']}")
        
        if 'findings' in data:
            findings = str(data['findings'])
            if len(findings) > 100:
                findings = findings[:100] + "..."
            parts.append(f"发现: {findings}")
        
        return '\n'.join(parts) if parts else "无关键信息"


# 全局格式化器实例
formatter = JinjaFormatter()


def format_json_response(response_data: Any, tool_name: str = '') -> str:
    """
    将JSON响应格式化为自然语言的便捷函数
    
    Args:
        response_data: JSON响应数据（字符串或字典）
        tool_name: 工具名称，用于选择合适的模板
    
    Returns:
        格式化后的自然语言描述
    """
    try:
        # 解析JSON字符串
        if isinstance(response_data, str):
            try:
                data = json.loads(response_data)
            except json.JSONDecodeError:
                return f"JSON解析失败: {response_data}"
        else:
            data = response_data
        
        # 使用Jinja2格式化
        return formatter.auto_format(data, tool_name)
        
    except Exception as e:
        return f"格式化失败: {str(e)}"


def create_custom_template(template_string: str) -> Template:
    """
    创建自定义模板
    
    Args:
        template_string: Jinja2模板字符串
    
    Returns:
        编译后的模板对象
    """
    return formatter.env.from_string(template_string)


# 示例使用
if __name__ == "__main__":
    # 测试数据
    test_data = {
        "step_number": 2,
        "total_steps": 5,
        "confidence": "medium",
        "findings": "发现了一些有趣的模式，需要进一步分析以确认假设的正确性。",
        "next_step_required": True
    }
    
    # 格式化测试
    result = format_json_response(test_data, "thinkdeep")
    print("格式化结果:")
    print(result)
