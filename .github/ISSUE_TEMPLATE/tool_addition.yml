name: 🛠️ New Gemini Tool Proposal
description: Propose a new Zen MCP tool (e.g., `summarize`, `fixer`, `refactor`)
labels: ["enhancement", "new-tool"]
body:
  - type: input
    id: tool-name
    attributes:
      label: Proposed Tool Name
      description: "What would the tool be called? (e.g., `summarize`, `docgen`, `refactor`)"
      placeholder: "e.g., `docgen`"
    validations:
      required: true

  - type: textarea
    id: purpose
    attributes:
      label: What is the primary purpose of this tool?
      description: "Explain the tool's core function and the value it provides to developers using Claude + Zen."
      placeholder: "This tool will automatically generate comprehensive documentation from code, extracting class and function signatures, docstrings, and creating usage examples."
    validations:
      required: true

  - type: textarea
    id: example-usage
    attributes:
      label: Example Usage in Claude Desktop
      description: "Show how a user would invoke this tool through <PERSON> and what the expected output would look like."
      placeholder: |
        **User prompt to Claude:**
        "Use zen to generate documentation for my entire src/ directory"

        **Expected behavior:**
        - Analyze all Python files in src/
        - Extract classes, functions, and their docstrings
        - Generate structured markdown documentation
        - Include usage examples where possible
        - Return organized documentation with table of contents
      render: markdown
    validations:
      required: true

  - type: dropdown
    id: tool-category
    attributes:
      label: Tool Category
      description: What category does this tool fit into?
      options:
        - Code Analysis (like analyze)
        - Code Quality (like codereview)
        - Code Generation/Refactoring
        - Documentation Generation
        - Testing Support
        - Debugging Support (like debug)
        - Workflow Automation
        - Architecture Planning (like thinkdeep)
        - Other
    validations:
      required: true

  - type: textarea
    id: system-prompt
    attributes:
      label: Proposed System Prompt (Optional)
      description: "If you have ideas for how zen should be prompted for this tool, share them here."
      placeholder: |
        You are an expert technical documentation generator. Your task is to create comprehensive, user-friendly documentation from source code...

  - type: checkboxes
    id: contribution
    attributes:
      label: Contribution
      options:
        - label: I am willing to submit a Pull Request to implement this new tool.
        - label: I have checked that this tool doesn't overlap significantly with existing tools (analyze, codereview, debug, thinkdeep, chat).

